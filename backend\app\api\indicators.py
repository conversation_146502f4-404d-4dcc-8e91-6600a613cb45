"""
Enhanced Multi-Indicator Management API Endpoints
Handles indicator configurations, calculations, and data retrieval
"""
from fastapi import APIRouter, HTTPException, Query, Body, Depends
from typing import Dict, List, Optional, Any
from datetime import datetime
import logging

from ..core.data_access import DataAccess
from ..services.indicator_config_manager import IndicatorConfigManager
from ..services.multi_indicator_engine import MultiIndicatorEngine
from ..core.exceptions import ValidationError, DatabaseError

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/v1/indicators", tags=["indicators"])

@router.get("/defaults")
async def get_indicator_defaults(indicator_name: Optional[str] = Query(None)):
    """
    Get default configurations for indicators

    Args:
        indicator_name: Optional specific indicator name

    Returns:
        Dictionary of default configurations
    """
    try:
        defaults = IndicatorConfigManager.get_indicator_defaults(indicator_name)

        if indicator_name and not defaults:
            raise HTTPException(status_code=404, detail=f"Indicator '{indicator_name}' not found")

        return {
            "success": True,
            "data": defaults,
            "message": f"Retrieved defaults for {indicator_name or 'all indicators'}"
        }

    except Exception as e:
        logger.error(f"Error getting indicator defaults: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/supported")
async def get_supported_indicators():
    """Get list of all supported indicators"""
    try:
        indicators = MultiIndicatorEngine.get_supported_indicators()
        defaults = IndicatorConfigManager.get_indicator_defaults()

        supported = []
        for indicator in indicators:
            default_info = defaults.get(indicator, {})
            supported.append({
                "name": indicator,
                "display_name": default_info.get("display_name", indicator),
                "description": default_info.get("description", ""),
                "chart_type": default_info.get("chart_type", "overlay"),
                "supports_multiple": default_info.get("supports_multiple", False)
            })

        return {
            "success": True,
            "data": supported,
            "count": len(supported)
        }

    except Exception as e:
        logger.error(f"Error getting supported indicators: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/strategies/{strategy_id}/indicators")
async def save_strategy_indicator_config(
    strategy_id: int,
    indicator_name: str = Body(...),
    config: Dict[str, Any] = Body(...),
    is_enabled: bool = Body(True),
    display_order: int = Body(0)
):
    """
    Save indicator configuration for a strategy

    Args:
        strategy_id: Strategy ID
        indicator_name: Indicator name
        config: Configuration dictionary
        is_enabled: Whether indicator is enabled
        display_order: Display order for UI

    Returns:
        Success response
    """
    try:
        success = IndicatorConfigManager.save_strategy_indicator_config(
            strategy_id=strategy_id,
            indicator_name=indicator_name,
            config=config,
            is_enabled=is_enabled,
            display_order=display_order
        )

        if not success:
            raise HTTPException(status_code=400, detail="Failed to save indicator configuration")

        return {
            "success": True,
            "message": f"Indicator '{indicator_name}' configuration saved for strategy {strategy_id}"
        }

    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except DatabaseError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logger.error(f"Error saving strategy indicator config: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/strategies/{strategy_id}/indicators")
async def get_strategy_indicator_configs(strategy_id: int):
    """
    Get all indicator configurations for a strategy

    Args:
        strategy_id: Strategy ID

    Returns:
        Dictionary of indicator configurations
    """
    try:
        configs = IndicatorConfigManager.get_strategy_indicator_configs(strategy_id)

        return {
            "success": True,
            "data": configs,
            "strategy_id": strategy_id,
            "count": len(configs)
        }

    except Exception as e:
        logger.error(f"Error getting strategy indicator configs: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/calculate")
async def calculate_indicators(
    symbol: str = Body(...),
    timeframe: str = Body(...),
    ohlcv_data: List[Dict[str, Any]] = Body(...),
    indicators_config: Optional[Dict[str, Dict]] = Body(None),
    strategy_id: Optional[int] = Body(None)
):
    """
    Calculate indicators for given OHLCV data

    Args:
        symbol: Trading symbol
        timeframe: Timeframe
        ohlcv_data: OHLCV data list
        indicators_config: Optional indicator configurations
        strategy_id: Optional strategy ID for configuration lookup

    Returns:
        Calculated indicator values
    """
    try:
        if not ohlcv_data:
            raise HTTPException(status_code=400, detail="OHLCV data is required")

        # If strategy_id provided but no config, get strategy indicators
        if strategy_id and not indicators_config:
            strategy_configs = IndicatorConfigManager.get_enabled_indicators_for_strategy(strategy_id)
            indicators_config = {name: config['config'] for name, config in strategy_configs.items()}

        # Calculate indicators
        results = MultiIndicatorEngine.calculate_all_indicators(
            ohlcv_data, indicators_config
        )

        return {
            "success": True,
            "data": results,
            "symbol": symbol,
            "timeframe": timeframe,
            "strategy_id": strategy_id,
            "data_points": len(ohlcv_data)
        }

    except Exception as e:
        logger.error(f"Error calculating indicators: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/strategies/{strategy_id}/indicators/{indicator_name}")
async def update_strategy_indicator_config(
    strategy_id: int,
    indicator_name: str,
    updates: Dict[str, Any] = Body(...)
):
    """
    Update specific fields of an indicator configuration

    Args:
        strategy_id: Strategy ID
        indicator_name: Indicator name
        updates: Dictionary of fields to update

    Returns:
        Success response
    """
    try:
        success = IndicatorConfigManager.update_strategy_indicator_config(
            strategy_id, indicator_name, updates
        )

        if not success:
            raise HTTPException(status_code=404, detail="Indicator configuration not found")

        return {
            "success": True,
            "message": f"Indicator '{indicator_name}' configuration updated for strategy {strategy_id}"
        }

    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except DatabaseError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logger.error(f"Error updating strategy indicator config: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/strategies/{strategy_id}/indicators/{indicator_name}")
async def delete_strategy_indicator_config(strategy_id: int, indicator_name: str):
    """
    Delete indicator configuration from strategy

    Args:
        strategy_id: Strategy ID
        indicator_name: Indicator name

    Returns:
        Success response
    """
    try:
        success = IndicatorConfigManager.delete_strategy_indicator_config(
            strategy_id, indicator_name
        )

        if not success:
            raise HTTPException(status_code=404, detail="Indicator configuration not found")

        return {
            "success": True,
            "message": f"Indicator '{indicator_name}' configuration deleted from strategy {strategy_id}"
        }

    except DatabaseError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logger.error(f"Error deleting strategy indicator config: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/data")
async def get_indicator_data(
    strategy_id: int = Query(...),
    symbol: str = Query(...),
    timeframe: str = Query(...),
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    indicator_names: Optional[List[str]] = Query(None)
):
    """
    Get calculated indicator data from database

    Args:
        strategy_id: Strategy ID
        symbol: Trading symbol
        timeframe: Timeframe
        start_date: Optional start date filter
        end_date: Optional end date filter
        indicator_names: Optional list of specific indicators

    Returns:
        Indicator data
    """
    try:
        # Get indicator data from database
        data = DataAccess.get_indicators_data(
            strategy_id=strategy_id,
            symbol=symbol,
            timeframe=timeframe,
            start_date=start_date,
            end_date=end_date,
            indicator_names=indicator_names
        )

        return {
            "success": True,
            "data": data,
            "strategy_id": strategy_id,
            "symbol": symbol,
            "timeframe": timeframe,
            "count": len(data)
        }

    except Exception as e:
        logger.error(f"Error getting indicator data: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/strategies/{strategy_id}/indicators/{indicator_name}/merged")
async def get_merged_indicator_config(strategy_id: int, indicator_name: str):
    """
    Get indicator configuration merged with defaults

    Args:
        strategy_id: Strategy ID
        indicator_name: Indicator name

    Returns:
        Merged configuration
    """
    try:
        config = IndicatorConfigManager.merge_with_defaults(strategy_id, indicator_name)

        return {
            "success": True,
            "data": config,
            "strategy_id": strategy_id,
            "indicator_name": indicator_name
        }

    except Exception as e:
        logger.error(f"Error getting merged indicator config: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/validate")
async def validate_indicator_config(
    indicator_name: str = Body(...),
    config: Dict[str, Any] = Body(...)
):
    """
    Validate indicator configuration

    Args:
        indicator_name: Indicator name
        config: Configuration to validate

    Returns:
        Validation result
    """
    try:
        is_valid = MultiIndicatorEngine.validate_config(indicator_name, config)

        return {
            "success": True,
            "valid": is_valid,
            "indicator_name": indicator_name,
            "message": "Configuration is valid" if is_valid else "Configuration is invalid"
        }

    except Exception as e:
        logger.error(f"Error validating indicator config: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/strategies/{strategy_id}/indicators/bulk")
async def bulk_save_strategy_indicators(
    strategy_id: int,
    indicators: Dict[str, Dict[str, Any]] = Body(...)
):
    """
    Save multiple indicator configurations for a strategy in bulk

    Args:
        strategy_id: Strategy ID
        indicators: Dictionary of indicator configurations

    Returns:
        Success response
    """
    try:
        success = IndicatorConfigManager.bulk_save_strategy_indicators(
            strategy_id, indicators
        )

        if not success:
            raise HTTPException(status_code=400, detail="Failed to save some indicator configurations")

        return {
            "success": True,
            "message": f"Bulk saved {len(indicators)} indicator configurations for strategy {strategy_id}"
        }

    except Exception as e:
        logger.error(f"Error in bulk save strategy indicators: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/snapshot")
async def get_indicator_snapshot(
    symbol: str = Body(...),
    timeframe: str = Body(...),
    timestamp: str = Body(...),
    ohlcv_data: List[Dict[str, Any]] = Body(...),
    indicators_config: Optional[Dict[str, Dict]] = Body(None),
    strategy_id: Optional[int] = Body(None)
):
    """Get indicator values at a specific timestamp"""
    try:
        target_timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))

        # If strategy_id provided but no config, get strategy indicators
        if strategy_id and not indicators_config:
            strategy_configs = IndicatorConfigManager.get_enabled_indicators_for_strategy(strategy_id)
            indicators_config = {name: config['config'] for name, config in strategy_configs.items()}

        # Calculate all indicators first
        all_indicators = MultiIndicatorEngine.calculate_all_indicators(ohlcv_data, indicators_config)

        # Get values at specific timestamp
        snapshot = MultiIndicatorEngine.get_indicator_values_at_timestamp(
            all_indicators, target_timestamp
        )

        return {
            "success": True,
            "data": {
                "symbol": symbol.upper(),
                "timeframe": timeframe,
                "timestamp": timestamp,
                "strategy_id": strategy_id,
                "snapshot": snapshot
            }
        }

    except Exception as e:
        logger.error(f"Error getting indicator snapshot: {e}")
        raise HTTPException(status_code=500, detail=str(e))
